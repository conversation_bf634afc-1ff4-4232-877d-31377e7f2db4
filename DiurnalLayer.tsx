import React, { useEffect, useRef } from 'react';

// Interface pour un nuage - VERSION CSS PURE
interface Cloud {
  id: number;
  x: number; // Position X initiale (pour être visible immédiatement)
  y: number; // Position Y aléatoire
  size: number; // Taille du nuage
  duration: number; // Durée de l'animation
  type: 'dust' | 'cloud-01' | 'cloud-02' | 'smoke';
}

// Interface pour les props du composant
interface DiurnalLayerProps {
  // Pas de props pour le moment
}

const DiurnalLayer: React.FC<DiurnalLayerProps> = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Générer les nuages - VERSION CSS PURE
  const generateClouds = (): Cloud[] => {
    const clouds: Cloud[] = [];
    const cloudCount = 8;

    for (let i = 0; i < cloudCount; i++) {
      const types: ('dust' | 'cloud-01' | 'cloud-02' | 'smoke')[] = ['dust', 'cloud-01', 'cloud-02', 'smoke'];

      clouds.push({
        id: i,
        x: Math.random() * 120 - 10, // Position X aléatoire sur tout l'écran (visible immédiatement)
        y: Math.random() * 70, // Position Y aléatoire de 0% à 70%
        size: 0.6 + Math.random() * 0.6, // Taille entre 0.6x et 1.2x
        duration: 40 + Math.random() * 30, // Durée entre 40s et 70s
        type: types[Math.floor(Math.random() * types.length)]
      });
    }

    return clouds;
  };





  // Initialiser les nuages - NOUVELLE VERSION PROPRE
  useEffect(() => {
    if (!containerRef.current) return;

    const clouds = generateClouds();

    // Créer les éléments DOM pour les nuages avec CSS pur
    clouds.forEach((cloud, index) => {
      const cloudElement = document.createElement('div');

      // Déterminer l'image du nuage selon le type
      let imageSrc: string;
      switch (cloud.type) {
        case 'dust':
          imageSrc = '/Cloud_white-dust-64.png';
          break;
        case 'cloud-01':
          imageSrc = '/Cloud-01.png';
          break;
        case 'cloud-02':
          imageSrc = '/cloud-02.png';
          break;
        case 'smoke':
          imageSrc = '/smoke-cloud-93.png';
          break;
        default:
          imageSrc = '/Cloud_white-dust-64.png';
      }

      // Style CSS complet pour le nuage
      cloudElement.style.cssText = `
        position: absolute;
        left: ${cloud.x}%;
        top: ${cloud.y}%;
        transform: scale(${cloud.size});
        pointer-events: none;
        z-index: 1;
        animation: moveCloud ${cloud.duration}s linear infinite;
      `;

      cloudElement.innerHTML = `
        <img
          src="${imageSrc}"
          alt="nuage"
          style="
            width: auto;
            height: 50px;
            opacity: 1.0;
            filter: drop-shadow(0 2px 6px rgba(0,0,0,0.2));
          "
          onload="console.log('✅ Nuage ${index + 1} chargé - X: ${cloud.x.toFixed(1)}%, Y: ${cloud.y.toFixed(1)}%')"
          onerror="console.error('❌ Erreur nuage:', '${imageSrc}')"
        />
      `;

      containerRef.current?.appendChild(cloudElement);
      console.log(`☁️ Nuage ${index + 1}/${clouds.length} créé - X: ${cloud.x.toFixed(1)}%, Y: ${cloud.y.toFixed(1)}%`);
    });

    // Nettoyage au démontage
    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 pointer-events-none overflow-hidden"
      style={{ zIndex: 2 }} // Au-dessus des étoiles (z-index 0) mais sous le contenu
    />
  );
};

export default DiurnalLayer;
