import React, { useEffect, useRef } from 'react';

// Interface pour un nuage - VERSION CSS PURE
interface Cloud {
  id: number;
  x: number; // Position X initiale (pour être visible immédiatement)
  y: number; // Position Y aléatoire
  size: number; // Taille du nuage
  duration: number; // Durée de l'animation
  type: 'dust' | 'cloud-01' | 'cloud-02' | 'smoke';
}

// Interface pour les props du composant
interface DiurnalLayerProps {
  // Pas de props pour le moment
}

const DiurnalLayer: React.FC<DiurnalLayerProps> = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Générer les nuages - VERSION CORRIGÉE
  const generateClouds = (): Cloud[] => {
    const clouds: Cloud[] = [];
    const cloudCount = 10;

    for (let i = 0; i < cloudCount; i++) {
      const types: ('dust' | 'cloud-01' | 'cloud-02' | 'smoke')[] = ['dust', 'cloud-01', 'cloud-02', 'smoke'];

      clouds.push({
        id: i,
        x: Math.random() * 100, // Position X aléatoire de 0% à 100% (visible immédiatement)
        y: Math.random() * 80, // Position Y aléatoire de 0% à 80% (plus de variété)
        size: 0.8 + Math.random() * 0.8, // Taille entre 0.8x et 1.6x (plus gros)
        duration: 80 + Math.random() * 60, // Durée entre 80s et 140s (plus lent)
        type: types[Math.floor(Math.random() * types.length)]
      });
    }

    return clouds;
  };





  // Initialiser les nuages - VERSION CORRIGÉE
  useEffect(() => {
    if (!containerRef.current) return;

    // Ajouter le style CSS pour l'animation directement dans le head
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      @keyframes moveCloudSlow {
        0% {
          transform: translateX(-20vw) scale(var(--cloud-scale));
        }
        100% {
          transform: translateX(120vw) scale(var(--cloud-scale));
        }
      }
    `;
    document.head.appendChild(styleElement);

    const clouds = generateClouds();

    // Créer les éléments DOM pour les nuages
    clouds.forEach((cloud, index) => {
      const cloudElement = document.createElement('div');

      // Déterminer l'image du nuage selon le type
      let imageSrc: string;
      switch (cloud.type) {
        case 'dust':
          imageSrc = '/Cloud_white-dust-64.png';
          break;
        case 'cloud-01':
          imageSrc = '/Cloud-01.png';
          break;
        case 'cloud-02':
          imageSrc = '/cloud-02.png';
          break;
        case 'smoke':
          imageSrc = '/smoke-cloud-93.png';
          break;
        default:
          imageSrc = '/Cloud_white-dust-64.png';
      }

      // Style CSS complet pour le nuage - SANS CONFLITS
      cloudElement.style.cssText = `
        position: absolute;
        left: ${cloud.x}%;
        top: ${cloud.y}%;
        --cloud-scale: ${cloud.size};
        pointer-events: none;
        z-index: 1;
        animation: moveCloudSlow ${cloud.duration}s linear infinite;
        transform-origin: center center;
      `;

      cloudElement.innerHTML = `
        <img
          src="${imageSrc}"
          alt="nuage"
          style="
            width: auto;
            height: 80px;
            opacity: 1.0;
            filter: drop-shadow(0 2px 6px rgba(0,0,0,0.2));
            display: block;
          "
          onload="console.log('✅ Nuage ${index + 1} chargé - X: ${cloud.x.toFixed(1)}%, Y: ${cloud.y.toFixed(1)}%')"
          onerror="console.error('❌ Erreur nuage:', '${imageSrc}')"
        />
      `;

      containerRef.current?.appendChild(cloudElement);
      console.log(`☁️ Nuage ${index + 1}/${clouds.length} créé - X: ${cloud.x.toFixed(1)}%, Y: ${cloud.y.toFixed(1)}%`);
    });

    // Nettoyage au démontage
    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
      // Supprimer le style ajouté
      document.head.removeChild(styleElement);
    };
  }, []);

  return (
    <>
      <style>
        {`
          @keyframes moveCloud {
            0% {
              left: -15%;
            }
            100% {
              left: 115%;
            }
          }
        `}
      </style>
      <div
        ref={containerRef}
        className="absolute inset-0 pointer-events-none overflow-hidden"
        style={{ zIndex: 2 }}
      />
    </>
  );
};

export default DiurnalLayer;
