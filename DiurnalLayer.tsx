import React, { useEffect, useRef } from 'react';

// Interface pour un nuage - VERSION CSS PURE
interface Cloud {
  id: number;
  x: number; // Position X initiale (pour être visible immédiatement)
  y: number; // Position Y aléatoire
  size: number; // Taille du nuage
  duration: number; // Durée de l'animation
  type: 'dust' | 'cloud-01' | 'cloud-02' | 'smoke';
}

// Interface pour les props du composant
interface DiurnalLayerProps {
  // Pas de props pour le moment
}

const DiurnalLayer: React.FC<DiurnalLayerProps> = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Générer les nuages - VERSION AVEC MÉLANGE ÉQUILIBRÉ DE TOUS LES TYPES
  const generateClouds = (): Cloud[] => {
    const clouds: Cloud[] = [];
    const cloudCount = 38; // 38 nuages pour un ciel bien rempli
    const types: ('dust' | 'cloud-01' | 'cloud-02' | 'smoke')[] = ['dust', 'cloud-01', 'cloud-02', 'smoke'];

    for (let i = 0; i < cloudCount; i++) {
      // Répartition équilibrée des types de nuages
      const typeIndex = i % types.length; // Cycle à travers tous les types
      const cloudType = types[typeIndex];

      clouds.push({
        id: i,
        x: Math.random() * 140 - 20, // Position X aléatoire de -20% à 120% (très bien répartis)
        y: Math.random() * 95, // Position Y aléatoire de 0% à 95% (toute la hauteur du ciel)
        size: 0.5 + Math.random() * 1.4, // Taille entre 0.5x et 1.9x (grande variété)
        duration: 350 + Math.random() * 250, // Durée entre 350s et 600s (6 à 10 minutes - encore plus paisible)
        type: cloudType
      });
    }

    // Mélanger l'ordre pour éviter les patterns
    for (let i = clouds.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [clouds[i], clouds[j]] = [clouds[j], clouds[i]];
    }

    return clouds;
  };





  // Initialiser les nuages - VERSION CORRIGÉE
  useEffect(() => {
    if (!containerRef.current) return;

    // Ajouter le style CSS pour l'animation directement dans le head
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      @keyframes moveCloudSlow {
        0% {
          transform: translateX(-25vw) scale(var(--cloud-scale));
        }
        100% {
          transform: translateX(125vw) scale(var(--cloud-scale));
        }
      }
    `;
    document.head.appendChild(styleElement);

    const clouds = generateClouds();

    // Créer les éléments DOM pour les nuages
    clouds.forEach((cloud, index) => {
      const cloudElement = document.createElement('div');

      // Déterminer l'image du nuage selon le type
      let imageSrc: string;
      switch (cloud.type) {
        case 'dust':
          imageSrc = '/Cloud_white-dust-64.png';
          break;
        case 'cloud-01':
          imageSrc = '/Cloud-01.png';
          break;
        case 'cloud-02':
          imageSrc = '/cloud-02.png';
          break;
        case 'smoke':
          imageSrc = '/smoke-cloud-93.png';
          break;
        default:
          imageSrc = '/Cloud_white-dust-64.png';
      }

      // Délai aléatoire pour répartir les nuages dans le temps
      const randomDelay = Math.random() * cloud.duration; // Délai entre 0 et la durée totale

      // Style CSS complet pour le nuage - AVEC DÉLAI ALÉATOIRE
      cloudElement.style.cssText = `
        position: absolute;
        left: ${cloud.x}%;
        top: ${cloud.y}%;
        --cloud-scale: ${cloud.size};
        pointer-events: none;
        z-index: 1;
        animation: moveCloudSlow ${cloud.duration}s linear infinite;
        animation-delay: -${randomDelay}s;
        transform-origin: center center;
      `;

      cloudElement.innerHTML = `
        <img
          src="${imageSrc}"
          alt="nuage"
          style="
            width: auto;
            height: 80px;
            opacity: 1.0;
            filter: drop-shadow(0 2px 6px rgba(0,0,0,0.2));
            display: block;
          "
          onload="console.log('✅ Nuage ${index + 1} chargé - X: ${cloud.x.toFixed(1)}%, Y: ${cloud.y.toFixed(1)}%')"
          onerror="console.error('❌ Erreur nuage:', '${imageSrc}')"
        />
      `;

      containerRef.current?.appendChild(cloudElement);
      console.log(`☁️ Nuage ${index + 1}/${clouds.length} créé - X: ${cloud.x.toFixed(1)}%, Y: ${cloud.y.toFixed(1)}%`);
    });

    // Nettoyage au démontage
    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
      // Supprimer le style ajouté
      document.head.removeChild(styleElement);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 pointer-events-none overflow-hidden"
      style={{ zIndex: 2 }}
    />
  );
};

export default DiurnalLayer;
