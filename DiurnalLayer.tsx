import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import * as SunCalc from 'suncalc';

// Interface pour un nuage - SIMPLIFIÉE
interface Cloud {
  id: number;
  y: number; // Position Y aléatoire (pourcentage)
  size: number; // Taille du nuage
  speed: number; // Vitesse de déplacement
  type: 'dust' | 'cloud-01' | 'cloud-02' | 'smoke'; // Types de nuages disponibles
}

// Interface pour les props du composant
interface DiurnalLayerProps {
  // Pas de props pour le moment
}

const DiurnalLayer: React.FC<DiurnalLayerProps> = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const cloudsRef = useRef<Cloud[]>([]);
  const animationsRef = useRef<gsap.core.Timeline[]>([]);

  // État pour la visibilité des éléments diurnes - COMMENCE VISIBLE
  const [diurnalOpacity, setDiurnalOpacity] = useState(1);

  // État pour la géolocalisation (même système que AstronomicalLayer)
  const [userLocation, setUserLocation] = useState<{lat: number, lon: number}>({
    lat: 48.8566, // Paris par défaut
    lon: 2.3522
  });
  const [locationReady, setLocationReady] = useState(false);

  // Générer les nuages - VERSION SIMPLIFIÉE
  const generateClouds = (): Cloud[] => {
    const clouds: Cloud[] = [];
    const cloudCount = 15; // Moins de nuages pour une animation plus claire

    for (let i = 0; i < cloudCount; i++) {
      const types: ('dust' | 'cloud-01' | 'cloud-02' | 'smoke')[] = ['dust', 'cloud-01', 'cloud-02', 'smoke'];

      clouds.push({
        id: i,
        y: Math.random() * 80, // Position Y aléatoire sur toute la hauteur (0% à 80%)
        size: 0.5 + Math.random() * 0.8, // Taille entre 0.5x et 1.3x
        speed: 0.3 + Math.random() * 0.4, // Vitesse lente entre 0.3 et 0.7
        type: types[Math.floor(Math.random() * types.length)]
      });
    }

    return clouds;
  };



  // Calculer l'opacité des éléments diurnes selon l'heure
  const calculateDiurnalOpacity = (currentTime: Date): number => {
    const sunTimes = SunCalc.getTimes(currentTime, userLocation.lat, userLocation.lon);
    
    const sunrise = sunTimes.sunrise.getHours() + sunTimes.sunrise.getMinutes() / 60;
    const sunset = sunTimes.sunset.getHours() + sunTimes.sunset.getMinutes() / 60;
    const currentHour = currentTime.getHours() + currentTime.getMinutes() / 60 + currentTime.getSeconds() / 3600;

    // Période de jour : éléments diurnes visibles
    if (currentHour >= sunrise + 0.5 && currentHour <= sunset - 0.5) {
      return 1.0;
    }

    // Transition progressive après le lever du soleil
    if (currentHour >= sunrise && currentHour < sunrise + 0.5) {
      const progress = (currentHour - sunrise) / 0.5;
      return progress;
    }

    // Transition progressive avant le coucher du soleil
    if (currentHour > sunset - 0.5 && currentHour <= sunset) {
      const progress = (currentHour - (sunset - 0.5)) / 0.5;
      return 1.0 - progress;
    }

    // Nuit : pas d'éléments diurnes
    return 0;
  };

  // Animation des nuages : SIMPLE - de gauche à droite avec positions initiales aléatoires
  const createCloudAnimation = (cloudElement: HTMLElement, cloud: Cloud) => {
    const startX = -10; // Commence hors écran à gauche
    const endX = 110;   // Finit hors écran à droite

    // Durée plus rapide pour voir l'animation
    const duration = 60 / cloud.speed; // Entre 86s et 200s selon la vitesse

    // Position initiale ALÉATOIRE sur l'écran pour avoir des nuages visibles immédiatement
    const initialX = Math.random() * 120 - 10; // Entre -10vw et 110vw

    // Position initiale du nuage - ALÉATOIRE sur l'écran
    gsap.set(cloudElement, {
      x: initialX + 'vw',
      y: cloud.y + '%',
      scale: cloud.size,
      opacity: 1
    });

    // Animation en boucle infinie
    const timeline = gsap.timeline({ repeat: -1 });

    // Si le nuage est déjà visible, on continue son trajet
    if (initialX < endX) {
      const remainingDistance = endX - initialX;
      const remainingDuration = (remainingDistance / (endX - startX)) * duration;

      timeline.to(cloudElement, {
        x: endX + 'vw',
        duration: remainingDuration,
        ease: "none"
      });
    }

    // Retour au début et nouveau cycle complet
    timeline.set(cloudElement, {
      x: startX + 'vw'
    });

    timeline.to(cloudElement, {
      x: endX + 'vw',
      duration: duration,
      ease: "none"
    });

    return timeline;
  };



  // Mettre à jour l'affichage diurne - TEMPORAIREMENT DÉSACTIVÉ POUR LES TESTS
  const updateDiurnalDisplay = () => {
    if (!locationReady) return;

    // TEMPORAIRE : Garder les nuages toujours visibles pour les tests
    console.log(`☀️ Éléments diurnes - Opacité forcée à 1.0 pour les tests`);
    setDiurnalOpacity(1.0);

    // Code original commenté pour les tests :
    // const now = new Date();
    // const newDiurnalOpacity = calculateDiurnalOpacity(now);
    // console.log(`☀️ Éléments diurnes - Opacité: ${newDiurnalOpacity.toFixed(2)}`);
    // setDiurnalOpacity(newDiurnalOpacity);
  };

  // Démarrer avec Paris par défaut
  useEffect(() => {
    setLocationReady(true);
  }, []);

  // Mise à jour dès que la géolocalisation est prête
  useEffect(() => {
    if (locationReady) {
      updateDiurnalDisplay();
    }
  }, [locationReady]);

  // Initialiser les nuages - VERSION SIMPLIFIÉE
  useEffect(() => {
    if (!containerRef.current) return;

    // Générer les nuages
    cloudsRef.current = generateClouds();

    // Créer les éléments DOM pour les nuages
    cloudsRef.current.forEach((cloud, index) => {
      const cloudElement = document.createElement('div');
      cloudElement.className = 'absolute pointer-events-none';
      cloudElement.style.willChange = 'transform, opacity';
      cloudElement.style.transform = 'translateZ(0)';
      cloudElement.setAttribute('data-cloud', 'true');

      // Déterminer l'image du nuage selon le type
      let imageSrc: string;
      switch (cloud.type) {
        case 'dust':
          imageSrc = '/Cloud_white-dust-64.png';
          break;
        case 'cloud-01':
          imageSrc = '/Cloud-01.png';
          break;
        case 'cloud-02':
          imageSrc = '/cloud-02.png';
          break;
        case 'smoke':
          imageSrc = '/smoke-cloud-93.png';
          break;
        default:
          imageSrc = '/Cloud_white-dust-64.png';
      }

      cloudElement.innerHTML = `
        <img
          src="${imageSrc}"
          alt="nuage"
          class="cloud-image"
          style="
            width: auto;
            height: ${cloud.size * 80}px;
            opacity: 1.0;
            filter: drop-shadow(0 2px 6px rgba(0,0,0,0.2));
          "
          onload="console.log('✅ Nuage chargé:', '${imageSrc}', 'Type:', '${cloud.type}')"
          onerror="console.error('❌ Erreur nuage:', '${imageSrc}')"
        />
      `;

      containerRef.current?.appendChild(cloudElement);

      // Créer et démarrer l'animation du nuage
      const cloudAnimation = createCloudAnimation(cloudElement, cloud);
      animationsRef.current.push(cloudAnimation);

      console.log(`☁️ Nuage ${index + 1}/${cloudsRef.current.length} créé - Y: ${cloud.y.toFixed(1)}%, Taille: ${cloud.size.toFixed(2)}x, Vitesse: ${cloud.speed.toFixed(2)}`);
    });

    // Mise à jour initiale de l'affichage
    setTimeout(updateDiurnalDisplay, 100);

    // Mise à jour toutes les secondes
    const interval = setInterval(updateDiurnalDisplay, 1000);

    // Nettoyage au démontage
    return () => {
      clearInterval(interval);
      animationsRef.current.forEach(animation => animation.kill());
      animationsRef.current = [];
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    };
  }, []);

  // Gérer l'opacité des nuages selon l'heure
  useEffect(() => {
    if (!containerRef.current) return;

    const cloudElements = containerRef.current.querySelectorAll('[data-cloud]');

    // Adapter l'apparence des nuages selon l'heure
    cloudElements.forEach((element) => {
      const cloudImg = element.querySelector('.cloud-image') as HTMLImageElement;
      if (cloudImg) {
        // Mode nocturne : nuages plus sombres et moins visibles
        if (diurnalOpacity < 0.3) {
          cloudImg.style.filter = 'drop-shadow(0 2px 6px rgba(0,0,0,0.4)) brightness(0.6) contrast(0.8)';
        } else {
          cloudImg.style.filter = 'drop-shadow(0 2px 6px rgba(0,0,0,0.2)) brightness(1.0) contrast(1.0)';
        }
      }

      // Animer l'opacité globale du nuage
      gsap.to(element, {
        opacity: diurnalOpacity,
        duration: 2,
        ease: "power2.out"
      });
    });

    // Gérer les animations selon la visibilité
    if (diurnalOpacity === 0) {
      animationsRef.current.forEach(animation => animation.pause());
    } else {
      animationsRef.current.forEach(animation => animation.resume());
    }
  }, [diurnalOpacity]);

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 pointer-events-none overflow-hidden"
      style={{ zIndex: 2 }} // Au-dessus des étoiles (z-index 0) mais sous le contenu
    />
  );
};

export default DiurnalLayer;
