import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import * as SunCalc from 'suncalc';

// Interface pour un nuage - NOUVELLE VERSION PROPRE
interface Cloud {
  id: number;
  x: number; // Position X initiale
  y: number; // Position Y aléatoire
  size: number; // Taille du nuage
  speed: number; // Vitesse de déplacement
  type: 'dust' | 'cloud-01' | 'cloud-02' | 'smoke';
}

// Interface pour les props du composant
interface DiurnalLayerProps {
  // Pas de props pour le moment
}

const DiurnalLayer: React.FC<DiurnalLayerProps> = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Générer les nuages - VERSION CORRIGÉE
  const generateClouds = (): Cloud[] => {
    const clouds: Cloud[] = [];
    const cloudCount = 10;

    for (let i = 0; i < cloudCount; i++) {
      const types: ('dust' | 'cloud-01' | 'cloud-02' | 'smoke')[] = ['dust', 'cloud-01', 'cloud-02', 'smoke'];

      clouds.push({
        id: i,
        x: -15 - (Math.random() * 50), // TOUS commencent hors écran à gauche (entre -15% et -65%)
        y: Math.random() * 70, // Position Y aléatoire de 0% à 70%
        size: 0.7 + Math.random() * 0.5, // Taille entre 0.7x et 1.2x
        speed: 30 + Math.random() * 20, // Vitesse entre 30s et 50s pour traverser l'écran
        type: types[Math.floor(Math.random() * types.length)]
      });
    }

    return clouds;
  };



  // Animation des nuages - VERSION CORRIGÉE SANS CONFLITS
  const createCloudAnimation = (cloudElement: HTMLElement, cloud: Cloud) => {
    // Position initiale du nuage - UTILISER SEULEMENT GSAP
    gsap.set(cloudElement, {
      x: cloud.x + '%',
      y: cloud.y + '%',
      scale: cloud.size,
      opacity: 1
    });

    // Animation simple : déplacement continu de gauche à droite
    gsap.to(cloudElement, {
      x: '120%', // Sort complètement de l'écran à droite
      duration: cloud.speed,
      ease: 'none',
      repeat: -1,
      onRepeat: () => {
        // Quand le nuage sort à droite, le remettre hors écran à gauche
        gsap.set(cloudElement, { x: '-15%' });
      }
    });
  };


  // Initialiser les nuages - NOUVELLE VERSION PROPRE
  useEffect(() => {
    if (!containerRef.current) return;

    const clouds = generateClouds();

    // Créer les éléments DOM pour les nuages
    clouds.forEach((cloud, index) => {
      const cloudElement = document.createElement('div');
      cloudElement.className = 'absolute pointer-events-none';
      cloudElement.style.zIndex = '1';

      // Déterminer l'image du nuage selon le type
      let imageSrc: string;
      switch (cloud.type) {
        case 'dust':
          imageSrc = '/Cloud_white-dust-64.png';
          break;
        case 'cloud-01':
          imageSrc = '/Cloud-01.png';
          break;
        case 'cloud-02':
          imageSrc = '/cloud-02.png';
          break;
        case 'smoke':
          imageSrc = '/smoke-cloud-93.png';
          break;
        default:
          imageSrc = '/Cloud_white-dust-64.png';
      }

      cloudElement.innerHTML = `
        <img
          src="${imageSrc}"
          alt="nuage"
          style="
            width: auto;
            height: 60px;
            opacity: 1.0;
            filter: drop-shadow(0 2px 6px rgba(0,0,0,0.2));
          "
          onload="console.log('✅ Nuage ${index + 1} chargé - Y: ${cloud.y.toFixed(1)}%')"
          onerror="console.error('❌ Erreur nuage:', '${imageSrc}')"
        />
      `;

      containerRef.current?.appendChild(cloudElement);

      // Créer l'animation du nuage
      createCloudAnimation(cloudElement, cloud);

      console.log(`☁️ Nuage ${index + 1}/${clouds.length} créé - X: ${cloud.x.toFixed(1)}%, Y: ${cloud.y.toFixed(1)}%`);
    });

    // Nettoyage au démontage
    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 pointer-events-none overflow-hidden"
      style={{ zIndex: 2 }} // Au-dessus des étoiles (z-index 0) mais sous le contenu
    />
  );
};

export default DiurnalLayer;
