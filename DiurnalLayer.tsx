import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import * as SunCalc from 'suncalc';

// Interface pour un nuage - NOUVELLE VERSION PROPRE
interface Cloud {
  id: number;
  x: number; // Position X initiale
  y: number; // Position Y aléatoire
  size: number; // Taille du nuage
  speed: number; // Vitesse de déplacement
  type: 'dust' | 'cloud-01' | 'cloud-02' | 'smoke';
}

// Interface pour les props du composant
interface DiurnalLayerProps {
  // Pas de props pour le moment
}

const DiurnalLayer: React.FC<DiurnalLayerProps> = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Générer les nuages - NOUVELLE VERSION PROPRE
  const generateClouds = (): Cloud[] => {
    const clouds: Cloud[] = [];
    const cloudCount = 12;

    for (let i = 0; i < cloudCount; i++) {
      const types: ('dust' | 'cloud-01' | 'cloud-02' | 'smoke')[] = ['dust', 'cloud-01', 'cloud-02', 'smoke'];

      clouds.push({
        id: i,
        x: Math.random() * 100, // Position X aléatoire sur tout l'écran
        y: Math.random() * 70, // Position Y aléatoire de 0% à 70%
        size: 0.6 + Math.random() * 0.6, // Taille entre 0.6x et 1.2x
        speed: 20 + Math.random() * 30, // Vitesse entre 20s et 50s pour traverser l'écran
        type: types[Math.floor(Math.random() * types.length)]
      });
    }

    return clouds;
  };



  // Animation des nuages - NOUVELLE VERSION SIMPLE ET PROPRE
  const createCloudAnimation = (cloudElement: HTMLElement, cloud: Cloud) => {
    // Position initiale du nuage
    gsap.set(cloudElement, {
      left: cloud.x + '%',
      top: cloud.y + '%',
      transform: `scale(${cloud.size})`,
      opacity: 1
    });

    // Animation simple : déplacement de gauche à droite
    gsap.to(cloudElement, {
      left: '110%',
      duration: cloud.speed,
      ease: 'none',
      repeat: -1,
      onRepeat: () => {
        // Quand le nuage sort de l'écran, le remettre à gauche
        gsap.set(cloudElement, { left: '-10%' });
      }
    });
  };


  // Initialiser les nuages - NOUVELLE VERSION PROPRE
  useEffect(() => {
    if (!containerRef.current) return;

    const clouds = generateClouds();

    // Créer les éléments DOM pour les nuages
    clouds.forEach((cloud, index) => {
      const cloudElement = document.createElement('div');
      cloudElement.className = 'absolute pointer-events-none';
      cloudElement.style.zIndex = '1';

      // Déterminer l'image du nuage selon le type
      let imageSrc: string;
      switch (cloud.type) {
        case 'dust':
          imageSrc = '/Cloud_white-dust-64.png';
          break;
        case 'cloud-01':
          imageSrc = '/Cloud-01.png';
          break;
        case 'cloud-02':
          imageSrc = '/cloud-02.png';
          break;
        case 'smoke':
          imageSrc = '/smoke-cloud-93.png';
          break;
        default:
          imageSrc = '/Cloud_white-dust-64.png';
      }

      cloudElement.innerHTML = `
        <img
          src="${imageSrc}"
          alt="nuage"
          style="
            width: auto;
            height: ${cloud.size * 60}px;
            opacity: 1.0;
            filter: drop-shadow(0 2px 6px rgba(0,0,0,0.2));
          "
          onload="console.log('✅ Nuage ${index + 1} chargé - Y: ${cloud.y.toFixed(1)}%')"
          onerror="console.error('❌ Erreur nuage:', '${imageSrc}')"
        />
      `;

      containerRef.current?.appendChild(cloudElement);

      // Créer l'animation du nuage
      createCloudAnimation(cloudElement, cloud);

      console.log(`☁️ Nuage ${index + 1}/${clouds.length} créé - X: ${cloud.x.toFixed(1)}%, Y: ${cloud.y.toFixed(1)}%`);
    });

    // Nettoyage au démontage
    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 pointer-events-none overflow-hidden"
      style={{ zIndex: 2 }} // Au-dessus des étoiles (z-index 0) mais sous le contenu
    />
  );
};

export default DiurnalLayer;
